package com.flowmining.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.Size;
import java.util.Map;

/**
 * 处理JSON数据的请求模型
 * 
 * <AUTHOR> Mining Analyzer
 * @version 1.0
 */
public class ProcessJsonRequest {
    
    /**
     * JSON数据内容
     */
    @JsonProperty("json_data")
    private Map<String, Object> jsonData;
    
    /**
     * 模块类型：auto（自动）、regular（常规）、simple（简单）
     */
    @JsonProperty("module_type")
    private String moduleType = "auto";
    
    /**
     * 输出格式：docx、pdf等
     */
    @JsonProperty("output_format")
    private String outputFormat = "docx";
    
    /**
     * 分析领域：如金融、医疗、物流等
     */
    private String domain;
    
    /**
     * 用户自定义的分析提示词（可选参数）
     * 当为空或未提供时，使用默认分析提示
     */
    @Size(max = 1000, message = "提示词长度不能超过1000个字符")
    private String prompt;
    
    // 默认分析提示词
    public static final String DEFAULT_PROMPT = "结合下面所有的数据进行深度分析并给出3条结论，结论中要有数据支撑，给出改善的意见";
    
    /**
     * 默认构造函数
     */
    public ProcessJsonRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param jsonData JSON数据
     * @param moduleType 模块类型
     * @param outputFormat 输出格式
     * @param domain 分析领域
     * @param prompt 分析提示词
     */
    public ProcessJsonRequest(Map<String, Object> jsonData, String moduleType, 
                             String outputFormat, String domain, String prompt) {
        this.jsonData = jsonData;
        this.moduleType = moduleType != null ? moduleType : "auto";
        this.outputFormat = outputFormat != null ? outputFormat : "docx";
        this.domain = domain;
        this.prompt = prompt;
    }
    
    // Getter和Setter方法
    
    public Map<String, Object> getJsonData() {
        return jsonData;
    }
    
    public void setJsonData(Map<String, Object> jsonData) {
        this.jsonData = jsonData;
    }
    
    public String getModuleType() {
        return moduleType;
    }
    
    public void setModuleType(String moduleType) {
        this.moduleType = moduleType != null ? moduleType : "auto";
    }
    
    public String getOutputFormat() {
        return outputFormat;
    }
    
    public void setOutputFormat(String outputFormat) {
        this.outputFormat = outputFormat != null ? outputFormat : "docx";
    }
    
    public String getDomain() {
        return domain;
    }
    
    public void setDomain(String domain) {
        this.domain = domain;
    }
    
    public String getPrompt() {
        return prompt;
    }
    
    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }
    
    /**
     * 获取有效的分析提示词
     * 如果用户提供了prompt，则使用用户的；否则使用默认提示词
     * 
     * @return 有效的分析提示词
     */
    public String getEffectivePrompt() {
        return (prompt != null && !prompt.trim().isEmpty()) ? prompt.trim() : DEFAULT_PROMPT;
    }
    
    /**
     * 验证请求数据的有效性
     * 
     * @return 验证结果消息，null表示验证通过
     */
    public String validate() {
        if (jsonData == null || jsonData.isEmpty()) {
            return "JSON数据不能为空";
        }
        
        if (prompt != null && prompt.length() > 1000) {
            return "提示词长度不能超过1000个字符";
        }
        
        return null; // 验证通过
    }
    
    @Override
    public String toString() {
        return "ProcessJsonRequest{" +
                "jsonData=" + (jsonData != null ? jsonData.size() + " items" : "null") +
                ", moduleType='" + moduleType + '\'' +
                ", outputFormat='" + outputFormat + '\'' +
                ", domain='" + domain + '\'' +
                ", prompt='" + (prompt != null ? prompt.substring(0, Math.min(50, prompt.length())) + "..." : "null") + '\'' +
                '}';
    }
}
